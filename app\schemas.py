from typing import Optional, List, Dict, Any, Literal
from datetime import datetime, date
from pydantic import BaseModel, EmailStr


class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"


class UserMe(BaseModel):
    _id: str
    email: EmailStr
    full_name: Optional[str] = None
    role: Literal["parent", "therapist"]
    client_ids: Optional[List[str]] = None


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


class ClientOut(BaseModel):
    _id: str
    name: str
    dob: Optional[date] = None
    therapist_id: Optional[str] = None
    parent_ids: Optional[List[str]] = None


class SessionCreate(BaseModel):
    client_id: str
    date: Optional[datetime] = None
    notes: Optional[str] = None
    goals: Optional[List[str]] = None
    metrics: Optional[Dict[str, float | int]] = None


class SessionOut(BaseModel):
    _id: str
    client_id: str
    date: datetime
    created_by: str
    notes: Optional[str] = None
    goals: Optional[List[str]] = None
    metrics: Optional[Dict[str, float | int]] = None


class ProgressPoint(BaseModel):
    date: date
    metrics: Dict[str, float | int]


class ProgressSeriesResponse(BaseModel):
    client_id: str
    points: List[ProgressPoint]


class ProgressChartResponse(BaseModel):
    client_id: str
    dates: List[date]
    series: Dict[str, List[float]]
