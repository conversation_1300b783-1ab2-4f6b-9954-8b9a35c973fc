from contextlib import asynccontextmanager
from fastapi import FastAPI
from app.db import connect_to_mongo, close_mongo_connection
from app.routers import (
    auth as auth_router,
    clients as clients_router,
    sessions as sessions_router,
    analytics as analytics_router,
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    await connect_to_mongo()
    try:
        yield
    finally:
        await close_mongo_connection()


app = FastAPI(title="ABA Client Portal API", version="0.1.0", lifespan=lifespan)


# Health/root endpoint
@app.get("/")
async def root():
    return {"message": "ABA Client Portal API is running"}


@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}


# Include feature routers
app.include_router(auth_router.router)
app.include_router(clients_router.router)
app.include_router(sessions_router.router)
app.include_router(analytics_router.router)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
